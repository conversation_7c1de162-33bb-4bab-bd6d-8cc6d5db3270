extends Button
class_name ItemSlot

## 物品槽UI组件
##
## 用于显示弹球或遗物的槽位，支持：
## - 显示物品图标
## - 空槽状态显示
## - 点击交互
## - 视觉反馈

# 信号定义
signal item_clicked(item_data: UIItemDataBase)
# UI节点引用
@onready var level_label: Label = $VBoxContainer/LevelLabel
@onready var texture_rect: TextureRect = $VBoxContainer/TextureRect
@onready var sweep_overlay: ColorRect = $SweepOverlay

# 数据存储
var item_data: UIItemDataBase
var is_empty: bool = true
var is_selected: bool = false
# 样式常量
const EMPTY_COLOR = Color(0.2, 0.2, 0.2, 1.0)
const FILLED_COLOR = Color.WHITE
const SELECTED_COLOR = Color(1.6, 1.6, 1.6, 1.0)


func _ready() -> void:
	# 设置初始状态
	set_empty()

	# 设置暂停模式以确保在游戏暂停时仍能接收输入
	process_mode = Node.PROCESS_MODE_WHEN_PAUSED


## 设置物品数据
## @param data: 物品数据字典，包含name、icon、description等信息
func set_item_data(data: UIItemDataBase) -> void:
	item_data = data
	is_empty = false

	# 更新显示
	var item_icon = data.get("icon")
	if item_icon:
		if item_icon is ShaderMaterial:
			texture_rect.texture = CanvasTexture.new()
			texture_rect.material = item_icon
		else:
			texture_rect.material = null
			texture_rect.texture = item_icon

	level_label.text = str(data.get("level"))
	# 更新按钮状态
	disabled = false
	self_modulate = FILLED_COLOR


## 设置为空槽状态
func set_empty() -> void:
	item_data = null
	is_empty = true

	# 更新显示
	level_label.text = ""
	texture_rect.texture = null
	texture_rect.material = null

	# 更新按钮状态
	disabled = true
	self_modulate = EMPTY_COLOR
	sweep_overlay.hide()


## 获取物品数据
func get_item_data() -> UIItemDataBase:
	return item_data


## 检查是否为空槽
func is_slot_empty() -> bool:
	return is_empty


## 设置选中状态
## @param selected: 是否选中
func set_selected(selected: bool) -> void:
	is_selected = selected

	if is_empty:
		return

	if selected:
		self_modulate = SELECTED_COLOR
		sweep_overlay.show()
	else:
		self_modulate = FILLED_COLOR
		sweep_overlay.hide()


## 检查是否被选中
func is_slot_selected() -> bool:
	return is_selected


## 槽位点击处理
func _on_slot_pressed() -> void:
	if not is_empty:
		print("物品槽被点击：", item_data.get("name"))
		item_clicked.emit(item_data)
	else:
		print("点击了空槽位")
