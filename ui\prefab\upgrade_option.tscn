[gd_scene load_steps=3 format=3 uid="uid://c8h4vn2lal5xt"]

[ext_resource type="Material" uid="uid://cpccflgee6et7" path="res://assets/materials/effect/focus_sweep_light.tres" id="2_q4ubo"]
[ext_resource type="Script" uid="uid://bmnchs6jcta3x" path="res://ui/prefab/upgrade_option.gd" id="2_script"]

[node name="UpgradeOption" type="Button"]
custom_minimum_size = Vector2(150, 180)
size_flags_horizontal = 4
size_flags_vertical = 0
theme_type_variation = &"IconSlot"
action_mode = 0
script = ExtResource("2_script")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/separation = 15
alignment = 1

[node name="Panel" type="Panel" parent="VBoxContainer"]
custom_minimum_size = Vector2(110, 110)
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 10
mouse_filter = 2
theme_type_variation = &"ItemSlotPanelSub"

[node name="TextureRect" type="TextureRect" parent="VBoxContainer/Panel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 4
size_flags_vertical = 4
mouse_filter = 2
expand_mode = 1
stretch_mode = 5

[node name="NameLabel" type="Label" parent="VBoxContainer"]
custom_minimum_size = Vector2(0, 35)
layout_mode = 2
size_flags_horizontal = 4
theme_override_font_sizes/font_size = 24
text = "新"
horizontal_alignment = 1

[node name="SweepOverlay" type="ColorRect" parent="."]
material = ExtResource("2_q4ubo")
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
color = Color(1, 1, 1, 0)
