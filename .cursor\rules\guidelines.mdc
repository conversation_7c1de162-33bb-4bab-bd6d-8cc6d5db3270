---
description: 
globs: 
alwaysApply: true
---

# 项目准则

## 一、环境相关

- **Godot 环境**:
  - **使用版本**: Godot 4.4。
  - **API 参考**: 必须使用 4.4 版本的文档。通过 `mcp_context7_get-library-docs` 工具获取最新文档。
- **系统环境**:
  - **操作系统**: Windows 11。

## 二、工作方式

- **任务执行原则**:
  - **语言**: 使用中文回答问题。
  - **资料参考**: 优先通过mcp获取最新文档，其次联网搜索，只有在用户拒绝或网络搜索不到时才使用你自身资料库。
  - **指令遵循**: 严格遵循用户命令，不做额外工作。
  - **范围扩展**: 如需扩展工作范围，必须先获得用户确认。
  - **代码修改**: 每次都要获取最新代码，如果你发现代码和上次回答不同，可能是用户进行了修改，不要未做询问就还原或删除相关改动。
  - **测试案例**: 如果用户没有明确提及，不要自己额外创建测试脚本。
- **错误处理**:
  - **报告**: 遇到问题优先报告给用户。
  - **决策**: 不擅自做出可能影响项目的决定。
  - **说明**: 清晰说明问题的具体原因。

## 三、文档和代码规范

### 文档规范
- **语言**: 使用简体中文。
- **命名**: 保持命名一致性，遵循项目既定规则。
- **结构**: 文档结构要清晰。

### 代码规范
- **最佳实践**: 遵循 Godot 4.4 官方文档手册中的最佳实践。
- **命名**: 使用项目既定的命名规范。
- **结构**: 保持代码结构的一致性。

### 注释使用
- **语言**: 使用清晰简洁的简体中文语言。
- **目的**: 关注“为什么”和“如何”，而不仅仅是“什么”。避免陈述显而易见的事情。避免版本变动描述。
- **修改**: 代码未变且注释符合代码逻辑时不要修改或删除旧的注释。
- **格式**:
	- **语言**: 使用简体中文注释。
    - **单行注释**: 用于简短解释。
    - **多行注释**: 用于较长的解释或函数/类描述。
    - **风格**: 确保注释采用 JSDoc3 风格。

## 四、工具使用

- **MCP 服务**:
  - **使用**: 优先使用项目提供的 MCP 服务。
  - **可用性**: 服务不可用时要明确提示。
  - **API**: 不要使用过时的 API 或方法。
- **路径处理**:
  - **格式**: 正确处理 Windows 路径格式。
  - **字符**: 注意路径中的中文字符处理。
  - **分隔符**: 使用正确的路径分隔符。

## 五、沟通原则

- **响应方式**:
  - **直接**: 直接回应用户的具体需求。
  - **范围**: 不主动扩展工作范围。
  - **疑问**: 如有疑问，直接询问用户。
- **问题报告**:
  - **描述**: 清晰描述问题。
  - **信息**: 提供具体的错误信息。
  - **指示**: 等待用户指示后继续。

